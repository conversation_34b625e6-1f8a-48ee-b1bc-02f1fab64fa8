require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
import express from 'express';
import bodyParser from 'body-parser';
import morgan from 'morgan';
import routes from './routes';
import errorHandler from './utils/errorHandler';
const https = require('https');
const http = require('http');
const cors = require('cors');
const fs = require("fs");
const path = require('path');
const compression = require('compression');

const app = express();

if(process.env.NODE_ENV !== 'production'){
  const mongoose = require('mongoose');
  const swaggerUi = require('swagger-ui-express');
  const generator = require('express-oas-generator');
  const modelNames = mongoose.modelNames();
  const { SPEC_OUTPUT_FILE_BEHAVIOR } = generator;
  generator.init(
    app, 
    function(spec) { return spec; },
    './swagger_specs.json',
    60 * 1000,
    'docs',
    modelNames,
    null,
    ['production'],
    false,
    SPEC_OUTPUT_FILE_BEHAVIOR.RECREATE
  );
  const swaggerData = fs.readFileSync("swagger_specs.json", 'utf8');
  const swaggerDocument = JSON.parse(swaggerData);
  
  app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));
}

const allowedOrigins = process.env.allowedOrigins ? process.env.allowedOrigins.split(',') : [];
const corsOptions = {
  origin: function (origin, callback) {
    if (allowedOrigins.indexOf(origin) !== -1 || process.env.NODE_ENV !== 'production') {
      callback(null, true);
    } else {
      
      callback(new Error('Not allowed by CORS'));
    }
  },
};

app.use('/files',express.static(process.env.FILES_PATH));
app.use(cors(corsOptions));
app.use(morgan('dev'));
app.use(compression())
app.use(bodyParser.urlencoded({extended: false, limit: '50mb'}));
app.use(bodyParser.json({limit: '50mb'}));
if (!fs.existsSync(process.env.FILES_PATH)) {
  fs.mkdirSync(process.env.FILES_PATH, { recursive: true} );
}

app.use(function (req, res, next) {
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET,PUT,HEAD,OPTIONS,POST,DELETE');
  res.header(
    'Access-Control-Allow-Headers',
    'Access-Control-Allow-Headers, Origin,Accept, X-Requested-With, Content-Type, Access-Control-Request-Method, Access-Control-Request-Headers, Authorization, Cache-Control',
  );
  next();
});
app.use('/api', routes);
app.use(errorHandler);

//const httpsOptions = {
 // key : process.env.IS_HTTPS === "true" ? fs.readFileSync(path.join(process.env.SSL_PATH, "winmed.key")) : "",
 // cert: process.env.IS_HTTPS === "true" ? fs.readFileSync(path.join(process.env.SSL_PATH, "winmed_ma.crt")) : ""
// }

// let server = process.env.IS_HTTPS === "true" ? https.createServer(httpsOptions,app) : http.createServer(app);
let server = http.createServer(app); // Ignore the SSL config
//fakeDataGenerator();
export default server;
